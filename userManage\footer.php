
  <script src="../assets/js/vendor.min.js"></script>
  <!-- Import Js Files -->
  <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
  <script src="../assets/js/theme/app.init.js"></script>
  <script src="../assets/js/theme/theme.js"></script>
  <script src="../assets/js/theme/app.min.js"></script>
  <script src="../assets/js/theme/sidebarmenu.js"></script>

  <!-- solar icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
  <!-- <script src="../assets/libs/fullcalendar/index.global.min.js"></script>
  <script src="../assets/js/apps/contact.js?v=1.0.2"></script>
  <script src="../assets/libs/prismjs/prism.js"></script> -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> <!-- Link to SweetAlert2 -->
  


<script>
// Define user role for JavaScript access
const currentUserRole = '<?php echo $_SESSION['role']; ?>';
const canResetPassword = (currentUserRole === 'admin' || currentUserRole === 'super_admin');

$(document).ready(function() {
    // Load users on page load
    loadUsers();

    // Search functionality
    $('#searchUsers').on('keyup', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterUsers(searchTerm);
    });

    // Role filter functionality
    $('.filter-role').on('click', function(e) {
        e.preventDefault();
        const role = $(this).data('role');
        filterUsersByRole(role);
    });

    // Add user form submission
    $('#addUserForm').on('submit', function(e) {
        e.preventDefault();
        addUser();
    });

    // Edit user form submission
    $('#editUserForm').on('submit', function(e) {
        e.preventDefault();
        updateUser();
    });
});

// Load all users
function loadUsers() {
    $('#loadingSpinner').show();
    $('#usersTableBody').empty();
    $('#noUsersMessage').hide();

    $.ajax({
        url: 'api/get_users.php',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            $('#loadingSpinner').hide();
            if (response.success && response.users.length > 0) {
                displayUsers(response.users);
            } else {
                $('#noUsersMessage').show();
            }
        },
        error: function() {
            $('#loadingSpinner').hide();
            Swal.fire('Error', 'Failed to load users', 'error');
        }
    });
}

// Display users in table
function displayUsers(users) {
    const tbody = $('#usersTableBody');
    tbody.empty();

    users.forEach(function(user) {
        const row = `
            <tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.name || 'N/A'}</td>
                <td>${user.email || 'N/A'}</td>
                <td>${getRoleBadge(user.role)}</td>
                <td>${getStatusBadge(user.status)}</td>
                <td>${formatDate(user.created_at)}</td>
                <td>
                    <!--
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editUser(${user.id})" title="Edit User">
                        <iconify-icon icon="solar:pen-line-duotone"></iconify-icon>
                    </button>
                    <button class="btn btn-sm btn-outline-warning me-1" onclick="resetUserPassword(${user.id})" title="Reset Password">
                        <iconify-icon icon="solar:key-minimalistic-line-duotone"></iconify-icon>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})" title="Delete User">
                        <iconify-icon icon="solar:trash-bin-minimalistic-line-duotone"></iconify-icon>
                    </button>
                    -->
                    <div class="action-btn">
                        <div class="dropdown dropstart">
                            <a href="javascript:void(0)" class="text-muted" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="ti ti-dots-vertical fs-6"></i>
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <li>
                                <a class="edit dropdown-item d-flex align-items-center gap-3" onclick="editUser(${user.id})" title="Edit User" href="javascript:void(0)">
                                    <i class="fs-4 ti ti-edit"></i>Edit
                                </a>
                            </li>
                            <li>
                                <a class="reset dropdown-item d-flex align-items-center gap-3" onclick="resetUserPassword(${user.id})" title="Reset Password" href="javascript:void(0)">
                                    <i class="fs-4 ti ti-key"></i>Reset Password
                                </a>
                            </li>
                            <li>
                                <a class="delete dropdown-item d-flex align-items-center gap-3" onclick="deleteUser(${user.id})" title="Delete User" href="javascript:void(0)">
                                    <i class="fs-4 ti ti-trash"></i>Delete
                                </a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// Get role badge HTML
function getRoleBadge(role) {
    switch(role) {
        case 'super_admin':
            return '<span class="badge bg-danger">Super Admin</span>';
        case 'admin':
            return '<span class="badge bg-warning">Admin</span>';
        case 'user':
            return '<span class="badge bg-success">User</span>';
        default:
            return '<span class="badge bg-secondary">Unknown</span>';
    }
}

// Get status badge HTML
function getStatusBadge(status) {
    return status == 1 ?
        '<span class="badge bg-success">Active</span>' :
        '<span class="badge bg-danger">Inactive</span>';
}

// Format date
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

// Filter users by search term
function filterUsers(searchTerm) {
    $('#usersTable tbody tr').each(function() {
        const row = $(this);
        const text = row.text().toLowerCase();
        row.toggle(text.includes(searchTerm));
    });
}

// Filter users by role
function filterUsersByRole(role) {
    if (role === 'all') {
        $('#usersTable tbody tr').show();
    } else {
        $('#usersTable tbody tr').each(function() {
            const row = $(this);
            const roleCell = row.find('td:nth-child(5)').text().toLowerCase();
            const showRow = roleCell.includes(role.replace('_', ' '));
            row.toggle(showRow);
        });
    }
}

// Add new user
function addUser() {
    const formData = new FormData($('#addUserForm')[0]);

    $.ajax({
        url: 'api/add_user.php',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'User Added Successfully!',
                    html: `
                        <p>${response.message}</p>
                        <div class="alert alert-info mt-3">
                            <strong>Default Password:</strong> <code>${response.default_password}</code><br>
                            <small>The user will be required to change this password on first login.</small>
                        </div>
                    `,
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
                $('#addUserModal').modal('hide');
                $('#addUserForm')[0].reset();
                loadUsers();
            } else {
                Swal.fire('Error', response.message || 'Failed to add user', 'error');
            }
        },
        error: function() {
            Swal.fire('Error', 'Failed to add user', 'error');
        }
    });
}

// Edit user
function editUser(userId) {
    $.ajax({
        url: 'api/get_user.php',
        method: 'GET',
        data: { id: userId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                const user = response.user;
                $('#editUserId').val(user.id);
                $('#editName').val(user.name);
                $('#editUsername').val(user.username);
                $('#editEmail').val(user.email);
                $('#editRole').val(user.role);
                $('#editStatus').val(user.status);
                $('#editUserModal').modal('show');
            } else {
                Swal.fire('Error', 'Failed to load user data', 'error');
            }
        },
        error: function() {
            Swal.fire('Error', 'Failed to load user data', 'error');
        }
    });
}

// Update user
function updateUser() {
    const formData = new FormData($('#editUserForm')[0]);

    $.ajax({
        url: 'api/update_user.php',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Swal.fire('Success', 'User updated successfully', 'success');
                $('#editUserModal').modal('hide');
                loadUsers();
            } else {
                Swal.fire('Error', response.message || 'Failed to update user', 'error');
            }
        },
        error: function() {
            Swal.fire('Error', 'Failed to update user', 'error');
        }
    });
}

// Delete user
function deleteUser(userId) {
    Swal.fire({
        title: 'Are you sure?',
        text: 'This action cannot be undone!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: 'api/delete_user.php',
                method: 'POST',
                data: { id: userId },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        Swal.fire('Deleted!', 'User has been deleted.', 'success');
                        loadUsers();
                    } else {
                        Swal.fire('Error', response.message || 'Failed to delete user', 'error');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Failed to delete user', 'error');
                }
            });
        }
    });
}

// Reset user password
function resetUserPassword(userId) {
    Swal.fire({
        title: 'Reset Password?',
        text: 'This will reset the user\'s password to the default password "pwd123" and require them to change it on next login.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#f39c12',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, reset password!'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: 'api/reset_password.php',
                method: 'POST',
                data: { id: userId },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Password Reset!',
                            html: `
                                <p>${response.message}</p>
                                <div class="alert alert-info mt-3">
                                    <strong>New Default Password:</strong> <code>${response.default_password}</code><br>
                                    <small>The user will be required to change this password on next login.</small>
                                </div>
                            `,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                        loadUsers();
                    } else {
                        Swal.fire('Error', response.message || 'Failed to reset password', 'error');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Failed to reset password', 'error');
                }
            });
        }
    });
}
</script>