/**
 * Datepicker Initialization Script
 * This script initializes the Bootstrap Datepicker for the floorplan date input
 */

$(document).ready(function() {
    console.log('Initializing datepicker...');

    // First, remove any existing datepicker to avoid duplicates
    try {
        $('#myDate').datepicker('remove');
    } catch (e) {
        console.log('No existing datepicker to remove');
    }

    // Initialize the datepicker with a single configuration
    $('#myDate').datepicker({
        format: 'dd-mm-yyyy',
        autoclose: true,
        todayHighlight: true,
        todayBtn: 'linked',
        clearBtn: true,
        orientation: 'bottom auto',
        language: 'en',
        zIndexOffset: 1000,
        startDate: new Date() // Prevent selection of past dates
    });

    // Add event listeners
    $('#myDate')
        .off('changeDate') // Remove any existing event handlers
        .on('changeDate', function(e) {
            // Explicitly trigger the change event when date is selected
            console.log('Date selected in datepicker:', $(this).val());
            $(this).trigger('change');
        });

    // Make the calendar icon clickable
    $('.input-group-text').on('click', function() {
        $('#myDate').datepicker('show');
    });

    // Debug the change event
    $('#myDate').on('change', function() {
        console.log('Change event fired on #myDate with value:', $(this).val());
    });

    // Use the global formatDateForAPI function if available
    // This avoids duplicate function declarations

    // Update table colors based on the floor
    function updateTableColors(floorNumber) {
        // Define colors for each floor
        const floorColors = {
            '1': {
                'A': '#539bff', // Blue
                'V': '#ffaa00'  // Yellow For VIP rooms
            },
            '2': {
                'B': '#539bff', // Blue
                'H': '#539bff', // Blue
                'V': '#ffaa00'  // Yellow For VIP rooms
            },
            '3': {
                'C': '#539bff' // Blue
            }
        };

        // Get all table rectangles in the current floor
        const floorId = `floor${floorNumber}`;
        const floorElement = document.getElementById(floorId);
        if (!floorElement) return;

        const tableRects = floorElement.querySelectorAll('rect[id^="rbox-"]');

        // Update the color of each table based on its row
        /*
        tableRects.forEach(rect => {
            const id = rect.getAttribute('id');
            const tableId = id.replace('rbox-', '');

            // Get the row identifier (first character of the table ID)
            let rowIdentifier;

            // For new format (A1/1, A2/2, etc.)
            if (tableId.includes('/')) {
                rowIdentifier = tableId.split('/')[0].charAt(0); // A, B, etc.
            }
            // For previous format (1A01, 2B03, etc.)
            else if (tableId.length >= 3 && /^\d/.test(tableId)) {
                rowIdentifier = tableId.charAt(1); // A, B, etc.
            }
            // For old format (A1, B2, etc.)
            else {
                rowIdentifier = tableId.charAt(0); // A, B, etc.
            }

            // Get the color for this row in this floor
            const color = floorColors[floorNumber] && floorColors[floorNumber][rowIdentifier];

            // Apply the color if it exists and the table is not booked or disabled
            if (color && !rect.parentElement.classList.contains('booked-disabled') && !rect.parentElement.classList.contains('table-disabled')) {
                rect.setAttribute('fill', color);
            }
        });
        */
    }
});
