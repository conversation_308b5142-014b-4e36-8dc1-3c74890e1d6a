<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}

require_once('../dbconnect/_dbconnect.php');

// Try to include mPDF library if available
$mpdfAvailable = false;
if (file_exists('../vendor/autoload.php')) {
    require_once('../vendor/autoload.php');
    $mpdfAvailable = class_exists('\Mpdf\Mpdf');
}

function generatePDF($date, $searchTerm = '') {
    try {
        // Get database connection
        $conn = db_connect();

        // Convert date format from dd-mm-yyyy to yyyy-mm-dd for database query
        $dateParts = explode('-', $date);
        if (count($dateParts) === 3) {
            $dbDate = $dateParts[2] . '-' . $dateParts[1] . '-' . $dateParts[0];
        } else {
            $dbDate = date('Y-m-d');
        }

        // Get bookings from database - Filter for 3rd floor only (use_zone = '3')
        $sql = "SELECT * FROM kp_booking WHERE use_date = :date AND use_zone = '3'";
        $params = [':date' => $dbDate];

        // Add search filter if provided
        if (!empty($searchTerm)) {
            $sql .= " AND (name LIKE :search OR phone LIKE :search OR orderNo LIKE :search)";
            $params[':search'] = '%' . $searchTerm . '%';
        }

        $sql .= " ORDER BY booking_id ASC";

        $stmt = $conn->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();

        $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Generate HTML content for PDF
        $html = generateHTMLContent($date, $bookings, $searchTerm);

        // Set filename for download
        $filename = "3rd_Floor_Bookings_" . str_replace('-', '_', $date);
        if (!empty($searchTerm)) {
            $filename .= "_filtered";
        }

        global $mpdfAvailable;

        if ($mpdfAvailable) {
            // Use mPDF if available
            try {
                $mpdf = new \Mpdf\Mpdf([
                    'mode' => 'utf-8',
                    'format' => 'A4-L', // A4 Landscape
                    'orientation' => 'L',
                    'margin_left' => 10,
                    'margin_right' => 10,
                    'margin_top' => 10,
                    'margin_bottom' => 10,
                    'margin_header' => 5,
                    'margin_footer' => 5
                ]);

                $mpdf->SetTitle('3rd Floor Bookings - ' . $date);
                $mpdf->SetAuthor('Sawasdee Booking System');
                $mpdf->SetSubject('Booking Report');

                // Write HTML to PDF
                $mpdf->WriteHTML($html);

                // Output PDF as download
                $mpdf->Output($filename . ".pdf", 'D'); // 'D' = Download
                return;

            } catch (Exception $mpdfError) {
                // Fall through to HTML fallback
            }
        }

        // Fallback: Output as HTML file for download
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.html"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        // Add print styles and auto-print functionality for HTML fallback
        $html = str_replace('</head>', '
        <style>
            @media print {
                .no-print { display: none !important; }
                body { -webkit-print-color-adjust: exact; }
            }
        </style>
        <script>
            // Auto-print when page loads (for HTML fallback)
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        </script>
        </head>', $html);

        echo $html;

    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
}

function generateHTMLContent($date, $bookings, $searchTerm) {
    $totalBookings = count($bookings);
    $totalAmount = 0;
    $totalAdults = 0;
    $totalChildren = 0;
    $totalInfants = 0;
    $totalGuide = 0;
    $totalFOC = 0;
    $totalTL = 0;

    // Process and sort tables from bookings
    $bookedTables = [];

    // Calculate totals and collect table data
    foreach ($bookings as $booking) {
        $totalAmount += floatval($booking['amount'] ?? 0);
        $totalAdults += intval($booking['adult'] ?? 0);
        $totalChildren += intval($booking['child'] ?? 0);
        $totalInfants += intval($booking['infant'] ?? 0);
        $totalGuide += intval($booking['guide'] ?? 0);
        $totalFOC += intval($booking['inspection'] ?? 0);
        $totalTL += intval($booking['team_leader'] ?? 0);

        // Process tables for this booking
        if (!empty($booking['tables'])) {
            $tables = explode(',', $booking['tables']);
            foreach ($tables as $table) {
                $table = trim($table);
                if (!empty($table)) {
                    $bookedTables[$table] = [
                        'name' => $booking['name'],
                        'adult' => intval($booking['adult'] ?? 0),
                        'child' => intval($booking['child'] ?? 0),
                        'special_request' => intval($booking['special_request'] ?? 0),
                        'agent' => $booking['agent'] ?? '',
                        'orderNo' => $booking['orderNo'] ?? '',
                        'remark' => $booking['remark'] ?? ''
                    ];
                }
            }
        }
    }

    // Sort tables by ID
    ksort($bookedTables);

    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Booking Report - ' . htmlspecialchars($date) . '</title>
    <style>
        @page {
            size: ' . $size . ' landscape;
            margin: 10mm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 18px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 9px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .summary {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .summary h3 {
            margin-top: 0;
            font-size: 14px;
        }
        .summary-grid-1 {
            display: grid;
            grid-template-columns: repeat(18, 1fr);
            gap: 10px;
            margin-top: 5px;
        }
        .summary-item {
            text-align: center;
            padding: 3px;
            background-color: #e8e8e8;
            border-radius: 3px;
            box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.1);
        }
        .summary-item-none {
            text-align: center;
            padding: 3px;
            // background-color: white;
            border-radius: 3px;
        }
        .summary-item-none2 {
            text-align: center;
            padding: 3px;
            background-color: white;
            border-radius: 3px;
        }    
        .summary-item strong {
            display: block;
            font-size: 13px;
            color: #007bff;
            margin-bottom: 2px;
        }
        .no-print {
            display: none;
        }
        .floorplan-section {
            margin-top: 20px;
            text-align: center;
        }
        .floorplan-image {
            width: 80%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Sawasdee Booking Report - 3rd Floor</h1>
        <p>Date: ' . htmlspecialchars($date) . '</p>
        <p>Exported on: ' . date('d-m-Y H:i:s') . '</p>
    </div>';
    
    // Generate dynamic floor plan based on booked tables
    $html .= '
    <div class="summary">
        <h3>Floor 3 - Booked Tables</h3>';

    if (empty($bookedTables)) {
        $html .= '<p style="text-align: center; padding: 20px;">No tables booked for this date.</p>';
    } else {

        $tableCount = 0;

        $tableIds = array(
            array('A19',   'A20',   'A21',   'A22',   'A23',   'A24',   'A25',   'A26',   'A27',   'A28',   'A29',   'A30',   'A31',   'A32',   'A33',   'A34',   'A35',   'A36'),
            array('A19-1', 'A20-1', 'A21-1', 'A22-1', 'A23-1', 'A24-1', 'A25-1', 'A26-1', 'A27-1', 'A28-1', 'A29-1', 'A30-1', 'A31-1', 'A32-1', 'A33-1', 'A34-1', 'A35-1', 'A36-1'),
            array('A19-2', 'A20-2', 'A21-2', 'A22-2', 'A23-2', 'A24-2', 'A25-2', 'A26-2', 'A27-2', 'A28-2', 'A29-2', 'A30-2', 'A31-2', 'A32-2', 'A33-2', 'A34-2', 'A35-2', 'A36-2'),
            array('A19-3', 'A20-3', 'A21-3', 'A22-3', 'A23-3', 'A24-3', 'A25-3', 'A26-3', 'A27-3', 'A28-3', 'A29-3', 'A30-3', 'A31-3', 'A32-3', 'A33-3', 'A34-3', 'x',     'x'),
            array('x',     'A20-4', 'A21-4', 'A22-4', 'A23-4', 'A24-4', 'A25-4', 'A26-4', 'A27-4', 'A28-4', 'A29-4', 'A30-4', 'A31-4', 'A32-4', 'A33-4', 'x',     'x',     'x'),
            array('x',     'A20-5', 'A21-5', 'A22-5', 'A23-5', 'A24-5', 'A25-5', 'A26-5', 'A27-5', 'A28-5', 'A29-5', 'A30-5', 'A31-5', 'A32-5', 'x',     'x',     'x',     'x'),
            array('x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x'),
            array('x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x'),
            array('x',     'A2-6',  'A3-6',  'A4-6',  'A5-6',  'A6-6',  'A7-6',  'A8-6',  'A9-6',  'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x',     'x'),
            array('A1-5',  'A2-5',  'A3-5',  'A4-5',  'A5-5',  'A6-5',  'A7-5',  'A8-5',  'A9-5',  'x',     'A10-5', 'A11-5', 'A12-5', 'A14-5', 'x',     'x',     'x',     'x'),
            array('A1-4',  'A2-4',  'A3-4',  'A4-4',  'A5-4',  'A6-4',  'A7-4',  'A8-4',  'A9-4',  'x',     'A10-4', 'A11-4', 'A12-4', 'A14-4', 'A15-4', 'A16-4', 'x',     'x'),
            array('A1-3',  'A2-3',  'A3-3',  'A4-3',  'A5-3',  'A6-3',  'A7-3',  'A8-3',  'A9-3',  'x',     'A10-3', 'A11-3', 'A12-3', 'A14-3', 'A15-3', 'A16-3', 'A17-3', 'x'),
            array('A1-2',  'A2-2',  'A3-2',  'A4-2',  'A5-2',  'A6-2',  'A7-2',  'A8-2',  'A9-2',  'x',     'A10-2', 'A11-2', 'A12-2', 'A14-2', 'A15-2', 'A16-2', 'A17-2', 'A18-2'),
            array('A1-1',  'A2-1',  'A3-1',  'A4-1',  'A5-1',  'A6-1',  'A7-1',  'A8-1',  'A9-1',  'x',     'A10-1', 'A11-1', 'A12-1', 'A14-1', 'A15-1', 'A16-1', 'A17-1', 'A18-1'),
            array('A1',    'A2',    'A3',    'A4',    'A5',    'A6',    'A7',    'A8',    'A9',    'x',     'A10',   'A11',   'A12',   'A14',   'A15',   'A16',   'A17',   'A18'),
        );

        $tableIdsNoneDisplay = array(
            "x"
        );

        foreach ($tableIds as $tableIdArray) {
            $html .= '<div class="summary-grid-1">';

            if ($tableCount > 0 && $tableCount % 11 == 0) {
                $html .= '</div><div class="summary-grid-1">';
            }

            foreach ($tableIdArray as $tableId) {
                if (array_key_exists($tableId, $bookedTables)) {
                    $tableData = $bookedTables[$tableId];
                    $totalPax = $tableData['adult'] + $tableData['child'];
                    $tableId = htmlspecialchars($tableId);
            
                    // Format special request icon
                    $specialIcon = '';
                    switch ($tableData['special_request']) {
                        case 1:
                            $specialIcon = '🎂';
                            break;
                        case 2:
                            $specialIcon = '🥂';
                            break;
                        default:
                            $specialIcon = '';
                    }

                    $html .= '
                    <div class="summary-item" style="width:80px; background-color:#e8e8e8;" id="' . htmlspecialchars($tableId) . '">
                        <span style="text-align: left; display: block; font-size: smaller">' . htmlspecialchars($tableData['orderNo']) . '</span>
                        <strong>' . htmlspecialchars($tableId) . '</strong>                        
                        <span>'.$specialIcon.' '.$totalPax.' Pax</span><br/>
                        <span>' . htmlspecialchars($tableData['name']) . '</span><br/>
                        <span>' . htmlspecialchars($tableData['remark']) . '</span><br/>
                        <span style="text-align: right; display: block; font-size: smaller;">' . htmlspecialchars($tableData['agent']) . '</span>
                    </div>';
                } else {

                    if (in_array($tableId, $tableIdsNoneDisplay)) {
                        $html .= '
                        <div class="summary-item-none" style="width:80px;">
                            <span>&nbsp;</span>                        
                        </div>';
                    } else {
                        $html .= '
                        <div class="summary-item" style="width:80px; background-color:#e8e8e8;" id="' . htmlspecialchars($tableId) . '">
                            <strong>' . htmlspecialchars($tableId) . '</strong>
                            <span>&nbsp;</span><br/>
                            <span>&nbsp;</span><br/>
                            <span>&nbsp;</span>
                        </div>';
                    }
                }
                $tableCount++;
            }

            $html .= '</div>';
        }
    }

    // Add summary section
    /*
    $html .= '
    <div class="summary">
        <h3>Summary</h3>
        <div class="summary-grid-1">
            <div class="summary-item">
                <strong>' . $totalBookings . '</strong>
                <span>Total Bookings</span>
            </div>
            <div class="summary-item">
                <strong>฿' . number_format($totalAmount, 2) . '</strong>
                <span>Total Amount</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalAdults . '</strong>
                <span>Total Adults</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalChildren . '</strong>
                <span>Total Children</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalInfants . '</strong>
                <span>Total Infants</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalGuide . '</strong>
                <span>Total Guide</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalFOC . '</strong>
                <span>Total FOC</span>
            </div>
            <div class="summary-item">
                <strong>' . $totalTL . '</strong>
                <span>Total TL</span>
            </div>
        </div>
    </div>';
    */

    $html .= '
</body>
</html>';

    return $html;
}

// Main execution
try {
    $date = $_GET['date'] ?? date('d-m-Y');
    $searchTerm = $_GET['search'] ?? '';
    
    generatePDF($date, $searchTerm);
    
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>