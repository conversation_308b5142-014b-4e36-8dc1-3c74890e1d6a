Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FEBA
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210285FF9, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E80  0002100690B4 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9160  00021006A49D (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCB18E0000 ntdll.dll
7FFC8A300000 aswhook.dll
7FFCB0E00000 KERNEL32.DLL
7FFCAED60000 KERNELBASE.dll
7FFCB0EE0000 USER32.dll
7FFCAF290000 win32u.dll
7FFCB1870000 GDI32.dll
7FFCAF150000 gdi32full.dll
000210040000 msys-2.0.dll
7FFCAECB0000 msvcp_win.dll
7FFCAEA30000 ucrtbase.dll
7FFCB0D40000 advapi32.dll
7FFCB0300000 msvcrt.dll
7FFCB0160000 sechost.dll
7FFCB1220000 RPCRT4.dll
7FFCAE130000 CRYPTBASE.DLL
7FFCAEB80000 bcryptPrimitives.dll
7FFCB0AD0000 IMM32.DLL
