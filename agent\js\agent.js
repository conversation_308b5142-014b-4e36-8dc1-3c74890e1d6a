/**
 * Agent Management JavaScript
 */

$(document).ready(function() {
    // Load agents on page load
    loadAgents();

    // Search functionality
    $('#input-search').on('keyup', function() {
        const searchTerm = $(this).val();
        if (searchTerm.length >= 2 || searchTerm.length === 0) {
            loadAgents(searchTerm);
        }
    });

    // Open modal for adding a new agent
    $('#btn-add-agent').on('click', function() {
        resetAgentForm();
        $('#agentModalTitle').text('Add New Agent');
        $('#agentModal').modal('show');
    });

    // Save agent (add or update)
    $('#btn-save-agent').on('click', function() {
        saveAgent();
    });

    // Initialize form validation
    $('#agentForm').on('submit', function(e) {
        e.preventDefault();
        saveAgent();
    });
});

/**
 * Load agents from the server
 * @param {string} search - Optional search term
 */
function loadAgents(search = '') {
    // Show loading message
    $('#agent-list-container').html('<tr><td colspan="7" class="text-center">Loading agents...</td></tr>');

    // Prepare URL with search parameter if provided
    let url = 'agent_api.php';
    if (search) {
        url += `?search=${encodeURIComponent(search)}`;
    }

    // Fetch agents from the server
    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                displayAgents(response.data);
            } else {
                showError('Failed to load agents: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            showError('Error loading agents: ' + error);
        }
    });
}

/**
 * Display agents in the table
 * @param {Array} agents - Array of agent objects
 */
function displayAgents(agents) {
    // Clear the table
    $('#agent-list-container').empty();

    // Check if there are any agents
    if (agents.length === 0) {
        $('#agent-list-container').html('<tr><td colspan="7" class="text-center">No agents found</td></tr>');
        return;
    }

    // Add each agent to the table
    agents.forEach(function(agent) {
        const statusBadge = agent.status == 1 
            ? '<span class="badge bg-success">Active</span>' 
            : '<span class="badge bg-danger">Inactive</span>';

        const row = `
            <tr>
                <td>${agent.id}</td>
                <td>${agent.name}</td>
                <td>${agent.contact_person || '-'}</td>
                <td>${agent.phone || '-'}</td>
                <td>${agent.email || '-'}</td>
                <td>${statusBadge}</td>
                <td>
                    <div class="action-btn">
                        <div class="dropdown dropstart">
                            <a href="javascript:void(0)" class="text-muted" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ti ti-dots-vertical fs-6"></i>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <li>
                                <a class="edit-agent dropdown-item d-flex align-items-center gap-3" href="javascript:void(0)" data-id="${agent.id}">
                                    <i class="fs-4 ti ti-edit"></i>Edit
                                </a>
                            </li>
                            <li>
                                <a class=" delete-agent dropdown-item d-flex align-items-center gap-3" href="javascript:void(0)" data-id="${agent.id}">
                                    <i class="fs-4 ti ti-trash"></i>Delete
                                </a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        `;

        $('#agent-list-container').append(row);
    });

    // Add event listeners for edit and delete buttons
    $('.edit-agent').on('click', function() {
        const agentId = $(this).data('id');
        editAgent(agentId);
    });

    $('.delete-agent').on('click', function() {
        const agentId = $(this).data('id');
        deleteAgent(agentId);
    });
}

/**
 * Reset the agent form
 */
function resetAgentForm() {
    $('#agentForm')[0].reset();
    $('#agent-id').val('0');
    $('#agent-status').val('1');
}

/**
 * Save agent (add or update)
 */
function saveAgent() {
    // Validate form
    if (!$('#agentForm')[0].checkValidity()) {
        $('#agentForm')[0].reportValidity();
        return;
    }

    // Get form data
    const agentId = $('#agent-id').val();
    const isNewAgent = agentId === '0';

    const agentData = {
        id: agentId,
        name: $('#agent-name').val(),
        contact_person: $('#agent-contact-person').val(),
        phone: $('#agent-phone').val(),
        email: $('#agent-email').val(),
        address: $('#agent-address').val(),
        status: $('#agent-status').val()
    };

    // Show loading indicator
    Swal.fire({
        title: isNewAgent ? 'Adding agent...' : 'Updating agent...',
        text: 'Please wait',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // Send request to server
    $.ajax({
        url: 'agent_api.php',
        type: isNewAgent ? 'POST' : 'PUT',
        dataType: 'json',
        data: JSON.stringify(agentData),
        contentType: 'application/json',
        success: function(response) {
            if (response.success) {
                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: response.message
                });

                // Close modal and reload agents
                $('#agentModal').modal('hide');
                loadAgents();
            } else {
                showError(response.message);
            }
        },
        error: function(xhr, status, error) {
            showError('Error saving agent: ' + error);
        }
    });
}

/**
 * Edit an agent
 * @param {number} agentId - The ID of the agent to edit
 */
function editAgent(agentId) {
    // Show loading indicator
    Swal.fire({
        title: 'Loading agent data...',
        text: 'Please wait',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // Fetch agent data from the server
    $.ajax({
        url: `agent_api.php?id=${agentId}`,
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            Swal.close();
            
            if (response.success) {
                const agent = response.data;
                
                // Populate form with agent data
                $('#agent-id').val(agent.id);
                $('#agent-name').val(agent.name);
                $('#agent-contact-person').val(agent.contact_person);
                $('#agent-phone').val(agent.phone);
                $('#agent-email').val(agent.email);
                $('#agent-address').val(agent.address);
                $('#agent-status').val(agent.status);
                
                // Update modal title and show modal
                $('#agentModalTitle').text('Edit Agent');
                $('#agentModal').modal('show');
            } else {
                showError('Failed to load agent data: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            Swal.close();
            showError('Error loading agent data: ' + error);
        }
    });
}

/**
 * Delete an agent
 * @param {number} agentId - The ID of the agent to delete
 */
function deleteAgent(agentId) {
    // Show confirmation dialog
    Swal.fire({
        title: 'Are you sure?',
        text: 'This will permanently delete the agent. This action cannot be undone.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading indicator
            Swal.fire({
                title: 'Deleting agent...',
                text: 'Please wait',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send delete request to server
            $.ajax({
                url: 'agent_api.php',
                type: 'DELETE',
                dataType: 'json',
                data: JSON.stringify({ id: agentId }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: response.message
                        });

                        // Reload agents
                        loadAgents();
                    } else {
                        showError(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    showError('Error deleting agent: ' + error);
                }
            });
        }
    });
}

/**
 * Show error message
 * @param {string} message - The error message to display
 */
function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'Error',
        text: message
    });
}
