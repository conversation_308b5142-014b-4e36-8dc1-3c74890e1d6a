<?php
// Autoload ของ Mpdf
spl_autoload_register(function ($class) {
    $prefix = 'Mpdf\\';
    $base_dir = __DIR__ . '/../mpdf/src/';

    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) return;

    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';

    if (file_exists($file)) require_once $file;
});

// Autoload ของ FPDI
spl_autoload_register(function ($class) {
    $prefix = 'setasign\\Fpdi\\';
    $base_dir = __DIR__ . '/../fpdi/src/';

    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) return;

    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';

    if (file_exists($file)) require_once $file;
});
