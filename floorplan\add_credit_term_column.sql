-- Add credit_term column to kp_booking table if it doesn't exist
-- This script is safe to run multiple times

-- Check and add credit_term column
SET @exists_credit_term = 0;
SELECT COUNT(*) INTO @exists_credit_term
FROM information_schema.columns
WHERE table_schema = DATABASE()
    AND table_name = 'kp_booking'
    AND column_name = 'credit_term';

SET @sql_credit_term = IF(
        @exists_credit_term = 0,
        'ALTER TABLE kp_booking ADD COLUMN credit_term TEXT DEFAULT NULL COMMENT "Credit term information"',
        'SELECT "Column credit_term already exists" as message'
    );

PREPARE stmt
FROM @sql_credit_term;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
