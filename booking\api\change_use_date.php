<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Include database connection
include '../../dbconnect/_dbconnect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get POST data
        $bookingId = isset($_POST['booking_id']) ? (int)$_POST['booking_id'] : 0;
        $newUseDate = isset($_POST['new_use_date']) ? $_POST['new_use_date'] : '';
        $adults = isset($_POST['adults']) ? (int)$_POST['adults'] : null;
        $children = isset($_POST['children']) ? (int)$_POST['children'] : null;
        $infants = isset($_POST['infants']) ? (int)$_POST['infants'] : null;

        // Validate input
        if ($bookingId <= 0) {
            throw new Exception('Invalid booking ID');
        }

        if (empty($newUseDate)) {
            throw new Exception('New use date is required');
        }

        // Validate date format
        $dateTime = DateTime::createFromFormat('Y-m-d', $newUseDate);
        if (!$dateTime || $dateTime->format('Y-m-d') !== $newUseDate) {
            throw new Exception('Invalid date format');
        }

        // Connect to database
        $conn = db_connect();

        // Check if booking exists
        $checkSql = "SELECT booking_id, orderNo, name, use_date, adult, child, infant FROM kp_booking WHERE booking_id = :booking_id";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bindParam(':booking_id', $bookingId, PDO::PARAM_INT);
        $checkStmt->execute();

        $booking = $checkStmt->fetch(PDO::FETCH_ASSOC);
        if (!$booking) {
            throw new Exception('Booking not found');
        }

        // Build update query dynamically based on provided fields
        $updateFields = ['use_date = :new_use_date'];
        $updateParams = [':new_use_date' => $newUseDate];

        if ($adults !== null) {
            $updateFields[] = 'adult = :adults';
            $updateParams[':adults'] = $adults;
        }

        if ($children !== null) {
            $updateFields[] = 'child = :children';
            $updateParams[':children'] = $children;
        }

        if ($infants !== null) {
            $updateFields[] = 'infant = :infants';
            $updateParams[':infants'] = $infants;
        }

        // Update the booking
        $updateSql = "UPDATE kp_booking SET " . implode(', ', $updateFields) . " WHERE booking_id = :booking_id";
        $updateStmt = $conn->prepare($updateSql);

        // Bind all parameters
        foreach ($updateParams as $param => $value) {
            $updateStmt->bindValue($param, $value);
        }
        $updateStmt->bindParam(':booking_id', $bookingId, PDO::PARAM_INT);
        
        if ($updateStmt->execute()) {
            // Log the change if logging is available
            if (function_exists('log_booking_edit')) {
                $changes = [
                    'use_date' => [
                        'old' => $booking['use_date'],
                        'new' => $newUseDate
                    ]
                ];

                // Add adults change if provided
                if ($adults !== null && $adults != $booking['adult']) {
                    $changes['adult'] = [
                        'old' => $booking['adult'],
                        'new' => $adults
                    ];
                }

                // Add children change if provided
                if ($children !== null && $children != $booking['child']) {
                    $changes['child'] = [
                        'old' => $booking['child'],
                        'new' => $children
                    ];
                }

                // Add infants change if provided
                if ($infants !== null && $infants != $booking['infant']) {
                    $changes['infant'] = [
                        'old' => $booking['infant'],
                        'new' => $infants
                    ];
                }

                $changeDescription = "Use date changed from {$booking['use_date']} to {$newUseDate}";
                if ($adults !== null || $children !== null || $infants !== null) {
                    $changeDescription .= " with updated guest counts";
                }

                log_booking_edit(
                    $bookingId,
                    $booking['orderNo'],
                    $_SESSION['id'] ?? 0,
                    $_SESSION['username'] ?? 'Unknown',
                    $_SESSION['name'] ?? 'Unknown User',
                    'UPDATE',
                    $changes,
                    $_SERVER['REMOTE_ADDR'] ?? '',
                    $_SERVER['HTTP_USER_AGENT'] ?? '',
                    $changeDescription
                );
            }

            echo json_encode([
                'success' => true,
                'message' => 'Use date changed successfully',
                'data' => [
                    'booking_id' => $bookingId,
                    'old_date' => $booking['use_date'],
                    'new_date' => $newUseDate
                ]
            ]);
        } else {
            throw new Exception('Failed to update use date');
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
}
?>
