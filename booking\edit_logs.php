<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}

require_once('../dbconnect/_dbconnect.php');
require_once('utils/BookingLogger.php');

// Check user permissions (only admin and super_admin can view logs)
require_once('../includes/role_check.php');
if (!isAdmin() && !isSuperAdmin()) {
    header('location:../dashboard');
    exit;
}

$pdo = db_connect();
$logger = new BookingLogger($pdo);

// Get filter parameters
$dateFrom = $_GET['date_from'] ?? date('Y-m-d', strtotime('-7 days'));
$dateTo = $_GET['date_to'] ?? date('Y-m-d');
$orderNo = $_GET['order_no'] ?? '';
$limit = intval($_GET['limit'] ?? 100);

// Get logs based on filters
if ($orderNo) {
    $logs = $logger->getLogsByOrderNo($orderNo, $limit);
} else {
    $logs = $logger->getRecentEditActivity($limit, $dateFrom, $dateTo);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Edit Logs</title>
    <link rel="stylesheet" href="../assets/css/styles.css" />
    <style>
        .log-entry {
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .log-action {
            padding: 2px 8px;
            border-radius: 3px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .action-UPDATE { background-color: #007bff; }
        .action-STATUS_CHANGE { background-color: #28a745; }
        .action-DELETE { background-color: #dc3545; }
        .field-changes {
            margin-top: 10px;
        }
        .field-change {
            margin-bottom: 5px;
            padding: 5px;
            background-color: #fff;
            border-left: 3px solid #007bff;
        }
        .old-value { color: #dc3545; text-decoration: line-through; }
        .new-value { color: #28a745; font-weight: bold; }
        .filter-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Booking Edit Logs</h4>
                        <p class="card-subtitle">Track all changes made to bookings</p>
                    </div>
                    <div class="card-body">
                        <!-- Filter Form -->
                        <form class="filter-form" method="GET">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="date_from" class="form-label">From Date</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?= htmlspecialchars($dateFrom) ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="date_to" class="form-label">To Date</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?= htmlspecialchars($dateTo) ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="order_no" class="form-label">Order Number (Optional)</label>
                                    <input type="text" class="form-control" id="order_no" name="order_no" value="<?= htmlspecialchars($orderNo) ?>" placeholder="Enter order number">
                                </div>
                                <div class="col-md-2">
                                    <label for="limit" class="form-label">Limit</label>
                                    <select class="form-control" id="limit" name="limit">
                                        <option value="50" <?= $limit == 50 ? 'selected' : '' ?>>50</option>
                                        <option value="100" <?= $limit == 100 ? 'selected' : '' ?>>100</option>
                                        <option value="200" <?= $limit == 200 ? 'selected' : '' ?>>200</option>
                                        <option value="500" <?= $limit == 500 ? 'selected' : '' ?>>500</option>
                                    </select>
                                </div>
                                <div class="col-md-1">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary form-control">Filter</button>
                                </div>
                            </div>
                        </form>

                        <!-- Logs Display -->
                        <div class="logs-container">
                            <?php if (empty($logs)): ?>
                                <div class="alert alert-info">
                                    <h5>No logs found</h5>
                                    <p>No edit logs found for the selected criteria.</p>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">Showing <?= count($logs) ?> log entries</p>
                                
                                <?php foreach ($logs as $log): ?>
                                    <div class="log-entry">
                                        <div class="log-header">
                                            <div>
                                                <strong>Booking ID: <?= htmlspecialchars($log['booking_id']) ?></strong>
                                                <?php if ($log['order_no']): ?>
                                                    | Order: <?= htmlspecialchars($log['order_no']) ?>
                                                <?php endif; ?>
                                                <span class="log-action action-<?= $log['action_type'] ?>"><?= $log['action_type'] ?></span>
                                            </div>
                                            <div class="text-muted">
                                                <?= date('d-m-Y H:i:s', strtotime($log['edit_timestamp'])) ?>
                                            </div>
                                        </div>
                                        
                                        <div class="log-details">
                                            <p><strong>Edited by:</strong> <?= htmlspecialchars($log['edited_by_name']) ?> (<?= htmlspecialchars($log['edited_by_username']) ?>)</p>
                                            
                                            <?php if ($log['ip_address']): ?>
                                                <p><strong>IP Address:</strong> <?= htmlspecialchars($log['ip_address']) ?></p>
                                            <?php endif; ?>
                                            
                                            <?php if ($log['notes']): ?>
                                                <p><strong>Notes:</strong> <?= htmlspecialchars($log['notes']) ?></p>
                                            <?php endif; ?>
                                            
                                            <?php if ($log['field_changes']): ?>
                                                <div class="field-changes">
                                                    <strong>Changes:</strong>
                                                    <?php 
                                                    $changes = BookingLogger::formatFieldChanges($log['field_changes']);
                                                    foreach ($changes as $change): 
                                                    ?>
                                                        <div class="field-change">
                                                            <strong><?= htmlspecialchars($change['label']) ?>:</strong>
                                                            <span class="old-value"><?= htmlspecialchars($change['old_value'] ?? 'Empty') ?></span>
                                                            →
                                                            <span class="new-value"><?= htmlspecialchars($change['new_value'] ?? 'Empty') ?></span>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/libs/jquery/dist/jquery.min.js"></script>
    <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
