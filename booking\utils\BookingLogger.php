<?php
/**
 * Booking Logger Class
 * 
 * Handles logging of all booking-related changes for audit trail
 */

class BookingLogger {
    private $pdo;
    
    public function __construct($pdo = null) {
        if ($pdo === null) {
            require_once dirname(__DIR__, 2) . '/dbconnect/_dbconnect.php';
            $this->pdo = db_connect();
        } else {
            $this->pdo = $pdo;
        }
    }
    
    /**
     * Log booking edit/update
     * 
     * @param int $bookingId Booking ID
     * @param string $orderNo Order number
     * @param array $oldData Original booking data
     * @param array $newData Updated booking data
     * @param string $actionType Type of action (UPDATE, STATUS_CHANGE, DELETE)
     * @param string $notes Additional notes
     * @return bool Success status
     */
    public function logBookingEdit($bookingId, $orderNo, $oldData, $newData, $actionType = 'UPDATE', $notes = '') {
        try {
            // Get current user information from session
            $userId = $_SESSION['id'] ?? 0;
            $username = $_SESSION['username'] ?? 'Unknown';
            $name = $_SESSION['name'] ?? 'Unknown User';
            
            // Get client information
            $ipAddress = $this->getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            // Calculate field changes
            $fieldChanges = $this->calculateFieldChanges($oldData, $newData);
            
            // Prepare SQL statement
            $sql = "INSERT INTO kp_booking_edit_log 
                    (booking_id, order_no, edited_by_user_id, edited_by_username, edited_by_name, 
                     action_type, field_changes, ip_address, user_agent, notes) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                $bookingId,
                $orderNo,
                $userId,
                $username,
                $name,
                $actionType,
                json_encode($fieldChanges),
                $ipAddress,
                $userAgent,
                $notes
            ]);
            
            if ($result) {
                error_log("Booking edit logged: Booking ID {$bookingId}, User: {$username}, Action: {$actionType}");
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Failed to log booking edit: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log payment status change
     * 
     * @param int $bookingId Booking ID
     * @param string $orderNo Order number
     * @param string $oldStatus Old payment status
     * @param string $newStatus New payment status
     * @param string $notes Additional notes
     * @return bool Success status
     */
    public function logPaymentStatusChange($bookingId, $orderNo, $oldStatus, $newStatus, $notes = '') {
        $oldData = ['payment_status' => $oldStatus];
        $newData = ['payment_status' => $newStatus];
        
        return $this->logBookingEdit($bookingId, $orderNo, $oldData, $newData, 'STATUS_CHANGE', $notes);
    }
    
    /**
     * Log booking deletion
     * 
     * @param int $bookingId Booking ID
     * @param string $orderNo Order number
     * @param array $bookingData Booking data before deletion
     * @param string $notes Additional notes
     * @return bool Success status
     */
    public function logBookingDeletion($bookingId, $orderNo, $bookingData, $notes = '') {
        return $this->logBookingEdit($bookingId, $orderNo, $bookingData, [], 'DELETE', $notes);
    }
    
    /**
     * Get edit history for a specific booking
     *
     * @param int $bookingId Booking ID
     * @param int $limit Number of records to return
     * @return array Edit history
     */
    public function getBookingEditHistory($bookingId, $limit = 50) {
        try {
            $sql = "SELECT * FROM kp_booking_edit_log
                    WHERE booking_id = ?
                    ORDER BY edit_timestamp DESC
                    LIMIT ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$bookingId, $limit]);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Failed to get booking edit history: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get edit history for a specific order number
     *
     * @param string $orderNo Order number
     * @param int $limit Number of records to return
     * @return array Edit history
     */
    public function getLogsByOrderNo($orderNo, $limit = 50) {
        try {
            $sql = "SELECT * FROM kp_booking_edit_log
                    WHERE order_no = ?
                    ORDER BY edit_timestamp DESC
                    LIMIT ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$orderNo, $limit]);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Failed to get logs by order number: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get recent edit activity
     * 
     * @param int $limit Number of records to return
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return array Recent edit activity
     */
    public function getRecentEditActivity($limit = 100, $dateFrom = null, $dateTo = null) {
        try {
            $sql = "SELECT * FROM kp_booking_edit_log WHERE 1=1";
            $params = [];
            
            if ($dateFrom) {
                $sql .= " AND DATE(edit_timestamp) >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $sql .= " AND DATE(edit_timestamp) <= ?";
                $params[] = $dateTo;
            }
            
            $sql .= " ORDER BY edit_timestamp DESC LIMIT ?";
            $params[] = $limit;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Failed to get recent edit activity: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Calculate field changes between old and new data
     * 
     * @param array $oldData Original data
     * @param array $newData Updated data
     * @return array Field changes
     */
    private function calculateFieldChanges($oldData, $newData) {
        $changes = [];
        
        // Define fields to track
        $fieldsToTrack = [
            'name', 'phone', 'adult', 'child', 'infant', 'guide', 
            'inspection', 'team_leader', 'voucher', 'agent', 'amount', 
            'remark', 'use_date', 'use_zone', 'tables', 'payment_status',
            'pay_type', 'special_request'
        ];
        
        foreach ($fieldsToTrack as $field) {
            $oldValue = $oldData[$field] ?? null;
            $newValue = $newData[$field] ?? null;
            
            // Convert to string for comparison
            $oldValueStr = (string)$oldValue;
            $newValueStr = (string)$newValue;
            
            if ($oldValueStr !== $newValueStr) {
                $changes[$field] = [
                    'old' => $oldValue,
                    'new' => $newValue
                ];
            }
        }
        
        return $changes;
    }
    
    /**
     * Get client IP address
     * 
     * @return string Client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    }
    
    /**
     * Format field changes for display
     * 
     * @param string $fieldChangesJson JSON string of field changes
     * @return array Formatted field changes
     */
    public static function formatFieldChanges($fieldChangesJson) {
        $changes = json_decode($fieldChangesJson, true);
        if (!$changes) {
            return [];
        }
        
        $formatted = [];
        $fieldLabels = [
            'name' => 'Customer Name',
            'phone' => 'Phone',
            'adult' => 'Adults',
            'child' => 'Children',
            'infant' => 'Infants',
            'guide' => 'Guide',
            'inspection' => 'FOC',
            'team_leader' => 'Team Leader',
            'voucher' => 'Voucher',
            'agent' => 'Agent',
            'amount' => 'Amount',
            'remark' => 'Remark',
            'use_date' => 'Date',
            'use_zone' => 'Floor',
            'tables' => 'Tables',
            'payment_status' => 'Payment Status',
            'pay_type' => 'Payment Type',
            'special_request' => 'Special Request'
        ];
        
        foreach ($changes as $field => $change) {
            $label = $fieldLabels[$field] ?? ucfirst($field);
            $formatted[] = [
                'field' => $field,
                'label' => $label,
                'old_value' => $change['old'],
                'new_value' => $change['new']
            ];
        }
        
        return $formatted;
    }
}
