-- Create booking edit log table
-- This table tracks all changes made to bookings

DROP TABLE IF EXISTS `kp_booking_edit_log`;

CREATE TABLE `kp_booking_edit_log` (
  `log_id` INT NOT NULL AUTO_INCREMENT,
  `booking_id` INT NOT NULL,
  `order_no` VARCHAR(50) DEFAULT NULL,
  `edited_by_user_id` INT NOT NULL,
  `edited_by_username` VARCHAR(50) NOT NULL,
  `edited_by_name` VARCHAR(100) DEFAULT NULL,
  `edit_timestamp` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `action_type` ENUM('UPDATE', 'STATUS_CHANGE', 'DELETE') NOT NULL DEFAULT 'UPDATE',
  `field_changes` JSON DEFAULT NULL COMMENT 'JSON object containing field changes: {"field_name": {"old": "old_value", "new": "new_value"}}',
  `ip_address` VARCHAR(45) DEFAULT NULL,
  `user_agent` TEXT DEFAULT NULL,
  `notes` TEXT DEFAULT NULL,
  PRIMARY KEY (`log_id`),
  INDEX `idx_booking_id` (`booking_id`),
  INDEX `idx_edited_by` (`edited_by_user_id`),
  INDEX `idx_edit_timestamp` (`edit_timestamp`),
  INDEX `idx_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add foreign key constraint if kp_booking table exists
-- ALTER TABLE `kp_booking_edit_log` 
-- ADD CONSTRAINT `fk_booking_edit_log_booking_id` 
-- FOREIGN KEY (`booking_id`) REFERENCES `kp_booking`(`booking_id`) ON DELETE CASCADE;

-- Add foreign key constraint if kp_login table exists  
-- ALTER TABLE `kp_booking_edit_log`
-- ADD CONSTRAINT `fk_booking_edit_log_user_id`
-- FOREIGN KEY (`edited_by_user_id`) REFERENCES `kp_login`(`id`) ON DELETE CASCADE;
